<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>茂名市地质灾害预警平台产品介绍</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        /* 基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #1E40AF;
            --secondary-color: #3B82F6;
            --accent-color: #06B6D4;
            --success-color: #10B981;
            --warning-color: #F59E0B;
            --danger-color: #EF4444;
            --text-primary: #111827;
            --text-secondary: #6B7280;
            --bg-primary: #FFFFFF;
            --bg-secondary: #F9FAFB;
            --border-color: #E5E7EB;
            --shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 25px rgba(0, 0, 0, 0.15);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow-x: hidden;
        }

        /* PPT容器 */
        .ppt-container {
            width: 100%;
            height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* 幻灯片样式 */
        .slide {
            width: 100%;
            height: 100vh;
            display: none;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 2rem;
            position: relative;
            background: var(--bg-primary);
            box-shadow: var(--shadow-lg);
        }

        .slide.active {
            display: flex;
        }

        /* 导航控制 */
        .nav-controls {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 1rem;
            z-index: 1000;
            background: rgba(255, 255, 255, 0.9);
            padding: 1rem;
            border-radius: 50px;
            backdrop-filter: blur(10px);
            box-shadow: var(--shadow);
        }

        .nav-btn {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 50%;
            background: var(--primary-color);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .nav-btn:hover {
            background: var(--secondary-color);
            transform: scale(1.1);
        }

        .nav-btn:disabled {
            background: var(--border-color);
            cursor: not-allowed;
            transform: none;
        }

        /* 幻灯片指示器 */
        .slide-indicators {
            position: fixed;
            bottom: 2rem;
            right: 2rem;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            z-index: 1000;
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .indicator.active {
            background: var(--primary-color);
            transform: scale(1.2);
        }

        /* 封面样式 */
        .cover-slide {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            text-align: center;
        }

        .cover-title {
            font-size: 3.5rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }

        .cover-subtitle {
            font-size: 1.5rem;
            margin-bottom: 2rem;
            opacity: 0.9;
        }

        .cover-info {
            font-size: 1.1rem;
            opacity: 0.8;
        }

        /* 内容页面样式 */
        .content-slide {
            padding: 3rem;
            max-width: 1200px;
            width: 100%;
        }

        .slide-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            margin-bottom: 2rem;
            text-align: center;
            position: relative;
        }

        .slide-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 100px;
            height: 4px;
            background: var(--accent-color);
            border-radius: 2px;
        }

        /* 卡片样式 */
        .card-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .card {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: var(--shadow);
            transition: all 0.3s ease;
            border: 1px solid var(--border-color);
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: var(--shadow-lg);
        }

        .card-icon {
            font-size: 3rem;
            color: var(--primary-color);
            margin-bottom: 1rem;
        }

        .card-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 1rem;
            color: var(--text-primary);
        }

        .card-content {
            color: var(--text-secondary);
            line-height: 1.6;
        }

        /* 图表容器 */
        .chart-container {
            width: 100%;
            height: 400px;
            margin: 2rem 0;
            background: var(--bg-primary);
            border-radius: 12px;
            box-shadow: var(--shadow);
            padding: 1rem;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .cover-title {
                font-size: 2.5rem;
            }

            .slide-title {
                font-size: 2rem;
            }

            .content-slide {
                padding: 1.5rem;
            }

            .card-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .nav-controls {
                bottom: 1rem;
                padding: 0.5rem;
            }

            .nav-btn {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
        }

        /* 动画效果 */
        @keyframes slideInFromRight {
            from {
                opacity: 0;
                transform: translateX(100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes slideInFromLeft {
            from {
                opacity: 0;
                transform: translateX(-100px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(50px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .animate-slide-in-right {
            animation: slideInFromRight 0.6s ease-out;
        }

        .animate-slide-in-left {
            animation: slideInFromLeft 0.6s ease-out;
        }

        .animate-fade-in-up {
            animation: fadeInUp 0.6s ease-out;
        }

        /* 特色样式 */
        .highlight-box {
            background: linear-gradient(135deg, var(--accent-color), var(--secondary-color));
            color: white;
            padding: 2rem;
            border-radius: 12px;
            margin: 2rem 0;
            text-align: center;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .stat-item {
            text-align: center;
            padding: 1.5rem;
            background: var(--bg-secondary);
            border-radius: 12px;
            border: 2px solid var(--border-color);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-color);
            display: block;
        }

        .stat-label {
            color: var(--text-secondary);
            margin-top: 0.5rem;
        }
    </style>
</head>
<body>
    <div class="ppt-container">
        <!-- 封面 -->
        <div class="slide cover-slide active" id="slide-0">
            <div class="animate-fade-in-up">
                <h1 class="cover-title">茂名市地质灾害预警平台</h1>
                <p class="cover-subtitle">产品介绍与展示</p>
                <div class="cover-info">
                    <p><i class="fas fa-building"></i> 茂名市自然资源勘探测绘院</p>
                    <p><i class="fas fa-calendar"></i> 2025年7月</p>
                    <p><i class="fas fa-shield-alt"></i> 守护茂名市民的地质安全</p>
                </div>
            </div>
        </div>

        <!-- 产品概述 -->
        <div class="slide content-slide" id="slide-1">
            <h2 class="slide-title animate-fade-in-up">产品概述</h2>
            <div class="stats-grid animate-slide-in-left">
                <div class="stat-item">
                    <span class="stat-number">74,215</span>
                    <div class="stat-label">地质灾害点和风险防范区</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">90</span>
                    <div class="stat-label">覆盖镇街</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">472万</span>
                    <div class="stat-label">服务人口</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">99.5%</span>
                    <div class="stat-label">系统可用性</div>
                </div>
            </div>
            <div class="highlight-box animate-slide-in-right">
                <h3>产品愿景</h3>
                <p>茂名市地质灾害预警平台致力于成为茂名市民身边的地质安全守护者，通过专业的数据管理和便民的查询服务，让地质灾害风险信息触手可及，让安全防护深入人心。</p>
            </div>
        </div>

        <!-- 核心功能 -->
        <div class="slide content-slide" id="slide-2">
            <h2 class="slide-title animate-fade-in-up">核心功能特性</h2>
            <div class="card-grid animate-slide-in-left">
                <div class="card">
                    <div class="card-icon"><i class="fas fa-search-location"></i></div>
                    <h3 class="card-title">公众查询服务</h3>
                    <div class="card-content">
                        <p>基于天地图服务的便民化查询体验，支持位置定位查询地质灾害区域范围，查询响应时间小于3秒。</p>
                        <ul style="margin-top: 1rem; padding-left: 1rem;">
                            <li>网站查询和微信公众号查询</li>
                            <li>专业化风险展示</li>
                            <li>预警信息服务</li>
                        </ul>
                    </div>
                </div>
                <div class="card">
                    <div class="card-icon"><i class="fas fa-database"></i></div>
                    <h3 class="card-title">数据管理模块</h3>
                    <div class="card-content">
                        <p>支持74,215个地质灾害点和风险防范区的完整生命周期管理，提升数据管理效率50%以上。</p>
                        <ul style="margin-top: 1rem; padding-left: 1rem;">
                            <li>地质灾害点管理</li>
                            <li>风险防范区管理</li>
                            <li>数据导入导出</li>
                        </ul>
                    </div>
                </div>
                <div class="card">
                    <div class="card-icon"><i class="fas fa-broadcast-tower"></i></div>
                    <h3 class="card-title">预警发布机制</h3>
                    <div class="card-content">
                        <p>建立多渠道预警发布机制，预警覆盖率达到95%以上，确保预警信息及时传达。</p>
                        <ul style="margin-top: 1rem; padding-left: 1rem;">
                            <li>微信公众号发布（优先）</li>
                            <li>网站公告发布</li>
                            <li>短信发布（应急补充）</li>
                        </ul>
                    </div>
                </div>
                <div class="card">
                    <div class="card-icon"><i class="fas fa-shield-alt"></i></div>
                    <h3 class="card-title">系统管理模块</h3>
                    <div class="card-content">
                        <p>建立完整的系统管理体系，确保平台的安全性、可管理性和可维护性。</p>
                        <ul style="margin-top: 1rem; padding-left: 1rem;">
                            <li>用户认证与授权</li>
                            <li>权限管理体系</li>
                            <li>操作审计机制</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 技术架构 -->
        <div class="slide content-slide" id="slide-3">
            <h2 class="slide-title animate-fade-in-up">技术架构</h2>
            <div class="chart-container animate-slide-in-left">
                <div id="techChart" style="width: 100%; height: 100%;"></div>
            </div>
            <div class="card-grid animate-slide-in-right">
                <div class="card">
                    <div class="card-icon"><i class="fas fa-layer-group"></i></div>
                    <h3 class="card-title">现代化技术栈</h3>
                    <div class="card-content">
                        <p><strong>前端：</strong>Vue3 + Element Plus + Vite</p>
                        <p><strong>后端：</strong>Python FastAPI + Nginx</p>
                        <p><strong>数据库：</strong>MySQL 8.0 + MongoDB 6.0</p>
                        <p><strong>地图服务：</strong>天地图API</p>
                    </div>
                </div>
                <div class="card">
                    <div class="card-icon"><i class="fas fa-cloud"></i></div>
                    <h3 class="card-title">基础设施配置</h3>
                    <div class="card-content">
                        <p><strong>服务器：</strong>16核64G云服务器</p>
                        <p><strong>存储：</strong>100GB系统盘 + 500GB数据盘</p>
                        <p><strong>网络：</strong>30Mbps固定带宽</p>
                        <p><strong>成本：</strong>9,676.8元至37,433.21元/年</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实施路线图 -->
        <div class="slide content-slide" id="slide-4">
            <h2 class="slide-title animate-fade-in-up">实施路线图</h2>
            <div class="chart-container animate-slide-in-left">
                <div id="roadmapChart" style="width: 100%; height: 100%;"></div>
            </div>
            <div class="card-grid animate-slide-in-right">
                <div class="card">
                    <div class="card-icon"><i class="fas fa-rocket"></i></div>
                    <h3 class="card-title">第一阶段（2周内）</h3>
                    <div class="card-content">
                        <p><strong>公众查询服务快速上线</strong></p>
                        <p>网站查询服务、查询结果展示、微信公众号集成</p>
                    </div>
                </div>
                <div class="card">
                    <div class="card-icon"><i class="fas fa-cogs"></i></div>
                    <h3 class="card-title">第二阶段（3-10周）</h3>
                    <div class="card-content">
                        <p><strong>系统管理和数据管理体系建设</strong></p>
                        <p>用户认证、权限管理、数据管理模块</p>
                    </div>
                </div>
                <div class="card">
                    <div class="card-icon"><i class="fas fa-broadcast-tower"></i></div>
                    <h3 class="card-title">第三阶段（10-13周）</h3>
                    <div class="card-content">
                        <p><strong>预警发布机制完善</strong></p>
                        <p>多渠道预警发布、操作日志管理</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 产品优势 -->
        <div class="slide content-slide" id="slide-5">
            <h2 class="slide-title animate-fade-in-up">产品优势</h2>
            <div class="card-grid animate-slide-in-left">
                <div class="card">
                    <div class="card-icon"><i class="fas fa-bullseye"></i></div>
                    <h3 class="card-title">专业化优势</h3>
                    <div class="card-content">
                        <p>专注地质灾害垂直领域，避免功能冗余，提供专业化的一体化解决方案，深度结合茂名市实际需求。</p>
                    </div>
                </div>
                <div class="card">
                    <div class="card-icon"><i class="fas fa-code"></i></div>
                    <h3 class="card-title">技术优势</h3>
                    <div class="card-content">
                        <p>采用现代化技术栈，轻量化设计，系统可用性达到99.5%以上，查询响应时间小于3秒。</p>
                    </div>
                </div>
                <div class="card">
                    <div class="card-icon"><i class="fas fa-users"></i></div>
                    <h3 class="card-title">用户体验优势</h3>
                    <div class="card-content">
                        <p>便民化设计，提供网站和微信公众号两种查询渠道，操作界面简洁直观，无需培训即可使用。</p>
                    </div>
                </div>
                <div class="card">
                    <div class="card-icon"><i class="fas fa-dollar-sign"></i></div>
                    <h3 class="card-title">成本优势</h3>
                    <div class="card-content">
                        <p>自主研发，运营成本可控，优先使用微信公众号等低成本预警渠道，轻量化设计降低运行成本。</p>
                    </div>
                </div>
            </div>
            <div class="chart-container animate-slide-in-right">
                <div id="advantageChart" style="width: 100%; height: 100%;"></div>
            </div>
        </div>

        <!-- 成功指标 -->
        <div class="slide content-slide" id="slide-6">
            <h2 class="slide-title animate-fade-in-up">成功指标与价值</h2>
            <div class="stats-grid animate-slide-in-left">
                <div class="stat-item">
                    <span class="stat-number">100%</span>
                    <div class="stat-label">查询服务功能完整性</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">95%+</span>
                    <div class="stat-label">预警信息发布覆盖率</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">50%+</span>
                    <div class="stat-label">数据管理效率提升</div>
                </div>
                <div class="stat-item">
                    <span class="stat-number">&lt;3秒</span>
                    <div class="stat-label">查询响应时间</div>
                </div>
            </div>
            <div class="chart-container animate-slide-in-right">
                <div id="valueChart" style="width: 100%; height: 100%;"></div>
            </div>
            <div class="highlight-box animate-fade-in-up">
                <h3>社会价值</h3>
                <p>提升公众安全防护能力，增强政府服务透明度和公信力，为茂名市数字政府建设贡献力量，具有重要的社会价值和现实意义。</p>
            </div>
        </div>

        <!-- 封底 -->
        <div class="slide cover-slide" id="slide-7">
            <div class="animate-fade-in-up">
                <h1 class="cover-title">谢谢观看</h1>
                <p class="cover-subtitle">让地质灾害风险信息触手可及<br>让安全防护深入人心</p>
                <div class="cover-info">
                    <p><i class="fas fa-envelope"></i> 联系邮箱：<EMAIL></p>
                    <p><i class="fas fa-phone"></i> 技术支持：茂名市自然资源勘探测绘院</p>
                    <p><i class="fas fa-heart"></i> 守护茂名，我们在行动</p>
                </div>
            </div>
        </div>
    </div>

    <!-- 导航控制 -->
    <div class="nav-controls">
        <button class="nav-btn" id="prevBtn" onclick="changeSlide(-1)">
            <i class="fas fa-chevron-left"></i>
        </button>
        <button class="nav-btn" id="nextBtn" onclick="changeSlide(1)">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <!-- 幻灯片指示器 -->
    <div class="slide-indicators">
        <div class="indicator active" onclick="goToSlide(0)"></div>
        <div class="indicator" onclick="goToSlide(1)"></div>
        <div class="indicator" onclick="goToSlide(2)"></div>
        <div class="indicator" onclick="goToSlide(3)"></div>
        <div class="indicator" onclick="goToSlide(4)"></div>
        <div class="indicator" onclick="goToSlide(5)"></div>
        <div class="indicator" onclick="goToSlide(6)"></div>
        <div class="indicator" onclick="goToSlide(7)"></div>
    </div>

    <script>
        let currentSlide = 0;
        const totalSlides = 8;

        // 幻灯片导航功能
        function changeSlide(direction) {
            const slides = document.querySelectorAll('.slide');
            const indicators = document.querySelectorAll('.indicator');

            slides[currentSlide].classList.remove('active');
            indicators[currentSlide].classList.remove('active');

            currentSlide += direction;

            if (currentSlide >= totalSlides) {
                currentSlide = 0;
            } else if (currentSlide < 0) {
                currentSlide = totalSlides - 1;
            }

            slides[currentSlide].classList.add('active');
            indicators[currentSlide].classList.add('active');

            updateNavButtons();
            initializeCharts();
        }

        function goToSlide(slideIndex) {
            const slides = document.querySelectorAll('.slide');
            const indicators = document.querySelectorAll('.indicator');

            slides[currentSlide].classList.remove('active');
            indicators[currentSlide].classList.remove('active');

            currentSlide = slideIndex;

            slides[currentSlide].classList.add('active');
            indicators[currentSlide].classList.add('active');

            updateNavButtons();
            initializeCharts();
        }

        function updateNavButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            prevBtn.disabled = false;
            nextBtn.disabled = false;
        }

        // 键盘导航
        document.addEventListener('keydown', function(event) {
            if (event.key === 'ArrowLeft') {
                changeSlide(-1);
            } else if (event.key === 'ArrowRight') {
                changeSlide(1);
            }
        });

        // 初始化图表
        function initializeCharts() {
            setTimeout(() => {
                if (currentSlide === 3) {
                    initTechChart();
                } else if (currentSlide === 4) {
                    initRoadmapChart();
                } else if (currentSlide === 5) {
                    initAdvantageChart();
                } else if (currentSlide === 6) {
                    initValueChart();
                }
            }, 100);
        }

        // 技术架构图表
        function initTechChart() {
            const chartDom = document.getElementById('techChart');
            if (!chartDom) return;

            const myChart = echarts.init(chartDom);
            const option = {
                title: {
                    text: '技术架构层次',
                    left: 'center',
                    textStyle: {
                        color: '#1E40AF',
                        fontSize: 18,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item'
                },
                series: [
                    {
                        name: '技术架构',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        center: ['50%', '60%'],
                        data: [
                            { value: 30, name: '表现层\n(Vue3 + Element Plus)', itemStyle: { color: '#3B82F6' } },
                            { value: 40, name: '业务逻辑层\n(Python FastAPI)', itemStyle: { color: '#1E40AF' } },
                            { value: 30, name: '数据存储层\n(MySQL + MongoDB)', itemStyle: { color: '#06B6D4' } }
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        label: {
                            show: true,
                            position: 'outside',
                            formatter: '{b}',
                            fontSize: 12,
                            color: '#111827'
                        }
                    }
                ]
            };
            myChart.setOption(option);
        }

        // 实施路线图表
        function initRoadmapChart() {
            const chartDom = document.getElementById('roadmapChart');
            if (!chartDom) return;

            const myChart = echarts.init(chartDom);
            const option = {
                title: {
                    text: '项目实施时间线',
                    left: 'center',
                    textStyle: {
                        color: '#1E40AF',
                        fontSize: 18,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['第1-2周', '第3-6周', '第7-10周', '第11-13周'],
                    axisLabel: {
                        color: '#6B7280'
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '完成度(%)',
                    axisLabel: {
                        color: '#6B7280'
                    }
                },
                series: [
                    {
                        name: '项目进度',
                        type: 'bar',
                        data: [
                            { value: 25, itemStyle: { color: '#10B981' } },
                            { value: 60, itemStyle: { color: '#3B82F6' } },
                            { value: 85, itemStyle: { color: '#F59E0B' } },
                            { value: 100, itemStyle: { color: '#1E40AF' } }
                        ],
                        barWidth: '60%',
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}%',
                            color: '#111827'
                        }
                    }
                ]
            };
            myChart.setOption(option);
        }

        // 产品优势雷达图
        function initAdvantageChart() {
            const chartDom = document.getElementById('advantageChart');
            if (!chartDom) return;

            const myChart = echarts.init(chartDom);
            const option = {
                title: {
                    text: '产品优势评估',
                    left: 'center',
                    textStyle: {
                        color: '#1E40AF',
                        fontSize: 18,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {},
                radar: {
                    indicator: [
                        { name: '专业化程度', max: 100 },
                        { name: '技术先进性', max: 100 },
                        { name: '用户体验', max: 100 },
                        { name: '成本控制', max: 100 },
                        { name: '安全性', max: 100 },
                        { name: '可维护性', max: 100 }
                    ],
                    center: ['50%', '60%'],
                    radius: '70%'
                },
                series: [
                    {
                        name: '产品优势',
                        type: 'radar',
                        data: [
                            {
                                value: [95, 90, 88, 85, 92, 87],
                                name: '茂名地质灾害预警平台',
                                itemStyle: {
                                    color: '#3B82F6'
                                },
                                areaStyle: {
                                    color: 'rgba(59, 130, 246, 0.3)'
                                }
                            }
                        ]
                    }
                ]
            };
            myChart.setOption(option);
        }

        // 价值分析图表
        function initValueChart() {
            const chartDom = document.getElementById('valueChart');
            if (!chartDom) return;

            const myChart = echarts.init(chartDom);
            const option = {
                title: {
                    text: '社会价值分析',
                    left: 'center',
                    textStyle: {
                        color: '#1E40AF',
                        fontSize: 18,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c}万人 ({d}%)'
                },
                series: [
                    {
                        name: '受益人群',
                        type: 'pie',
                        radius: ['30%', '70%'],
                        center: ['50%', '60%'],
                        data: [
                            { value: 472, name: '直接受益群众', itemStyle: { color: '#10B981' } },
                            { value: 100, name: '政府工作人员', itemStyle: { color: '#3B82F6' } },
                            { value: 50, name: '基层工作人员', itemStyle: { color: '#F59E0B' } },
                            { value: 30, name: '相关从业人员', itemStyle: { color: '#06B6D4' } }
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        label: {
                            show: true,
                            position: 'outside',
                            formatter: '{b}\n{c}万人',
                            fontSize: 12,
                            color: '#111827'
                        }
                    }
                ]
            };
            myChart.setOption(option);
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateNavButtons();
            initializeCharts();

            // 响应式处理
            window.addEventListener('resize', function() {
                setTimeout(() => {
                    initializeCharts();
                }, 100);
            });
        });

        // 自动播放功能（可选）
        let autoPlay = false;
        let autoPlayInterval;

        function toggleAutoPlay() {
            if (autoPlay) {
                clearInterval(autoPlayInterval);
                autoPlay = false;
            } else {
                autoPlayInterval = setInterval(() => {
                    changeSlide(1);
                }, 5000);
                autoPlay = true;
            }
        }

        // 添加双击切换自动播放
        document.addEventListener('dblclick', toggleAutoPlay);
    </script>
</body>
</html>

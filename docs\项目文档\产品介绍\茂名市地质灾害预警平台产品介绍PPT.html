<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>茂名市地质灾害预警平台产品介绍</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
    <style>
        /* 全局重置和基础样式 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            /* 主色调 - 政务蓝 */
            --primary-blue: #1E40AF;
            --secondary-blue: #3B82F6;
            --light-blue: #DBEAFE;
            --accent-blue: #06B6D4;

            /* 辅助色彩 */
            --success-green: #10B981;
            --warning-orange: #F59E0B;
            --danger-red: #EF4444;

            /* 中性色 */
            --text-primary: #1F2937;
            --text-secondary: #6B7280;
            --text-light: #9CA3AF;
            --bg-white: #FFFFFF;
            --bg-gray: #F9FAFB;
            --border-gray: #E5E7EB;

            /* 阴影 */
            --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
            --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            line-height: 1.6;
            color: var(--text-primary);
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            overflow: hidden;
        }

        /* PPT容器 */
        .presentation-container {
            width: 100vw;
            height: 100vh;
            position: relative;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        /* 幻灯片 */
        .slide {
            width: 90vw;
            max-width: 1200px;
            height: 85vh;
            background: var(--bg-white);
            border-radius: 16px;
            box-shadow: var(--shadow-xl);
            display: none;
            position: relative;
            overflow: hidden;
        }

        .slide.active {
            display: block;
            animation: slideIn 0.5s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 幻灯片内容区域 */
        .slide-content {
            padding: 60px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        /* 标题样式 */
        .slide-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary-blue);
            margin-bottom: 2rem;
            text-align: center;
            position: relative;
        }

        .slide-title::after {
            content: '';
            position: absolute;
            bottom: -8px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, var(--accent-blue), var(--secondary-blue));
            border-radius: 2px;
        }

        .slide-subtitle {
            font-size: 1.2rem;
            color: var(--text-secondary);
            text-align: center;
            margin-bottom: 3rem;
        }

        /* 封面样式 */
        .cover-slide {
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--secondary-blue) 100%);
            color: white;
        }

        .cover-slide .slide-content {
            justify-content: center;
            align-items: center;
            text-align: center;
        }

        .cover-title {
            font-size: 3.5rem;
            font-weight: 800;
            margin-bottom: 1.5rem;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
        }

        .cover-subtitle {
            font-size: 1.5rem;
            margin-bottom: 3rem;
            opacity: 0.9;
        }

        .cover-info {
            display: flex;
            flex-direction: column;
            gap: 1rem;
            font-size: 1.1rem;
            opacity: 0.8;
        }

        .cover-info i {
            margin-right: 0.5rem;
            width: 20px;
        }

        /* 内容布局 */
        .content-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            flex: 1;
        }

        .content-card {
            background: var(--bg-white);
            border: 1px solid var(--border-gray);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: var(--shadow-sm);
            transition: all 0.3s ease;
            position: relative;
        }

        .content-card:hover {
            transform: translateY(-4px);
            box-shadow: var(--shadow-lg);
        }

        .card-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, var(--secondary-blue), var(--accent-blue));
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 1.5rem;
        }

        .card-icon i {
            font-size: 1.8rem;
            color: white;
        }

        .card-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: 1rem;
        }

        .card-description {
            color: var(--text-secondary);
            line-height: 1.6;
            margin-bottom: 1rem;
        }

        .card-features {
            list-style: none;
            padding: 0;
        }

        .card-features li {
            color: var(--text-secondary);
            margin-bottom: 0.5rem;
            position: relative;
            padding-left: 1.5rem;
        }

        .card-features li::before {
            content: '✓';
            position: absolute;
            left: 0;
            color: var(--success-green);
            font-weight: bold;
        }

        /* 统计数据样式 */
        .stats-container {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .stat-item {
            text-align: center;
            padding: 2rem;
            background: var(--bg-gray);
            border-radius: 12px;
            border: 2px solid var(--border-gray);
            transition: all 0.3s ease;
        }

        .stat-item:hover {
            border-color: var(--secondary-blue);
            transform: translateY(-2px);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 800;
            color: var(--primary-blue);
            display: block;
            margin-bottom: 0.5rem;
        }

        .stat-label {
            color: var(--text-secondary);
            font-size: 0.9rem;
        }

        /* 图表容器 */
        .chart-container {
            background: var(--bg-white);
            border-radius: 12px;
            padding: 1.5rem;
            box-shadow: var(--shadow-sm);
            margin: 2rem 0;
            height: 400px;
        }

        /* 导航控制 */
        .navigation {
            position: fixed;
            bottom: 2rem;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            align-items: center;
            gap: 1rem;
            background: rgba(255, 255, 255, 0.95);
            padding: 1rem 2rem;
            border-radius: 50px;
            box-shadow: var(--shadow-lg);
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .nav-button {
            width: 48px;
            height: 48px;
            border: none;
            border-radius: 50%;
            background: var(--primary-blue);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.2rem;
            transition: all 0.3s ease;
        }

        .nav-button:hover {
            background: var(--secondary-blue);
            transform: scale(1.05);
        }

        .nav-button:disabled {
            background: var(--border-gray);
            cursor: not-allowed;
            transform: none;
        }

        .slide-counter {
            font-size: 0.9rem;
            color: var(--text-secondary);
            font-weight: 500;
            min-width: 60px;
            text-align: center;
        }

        /* 幻灯片指示器 */
        .slide-indicators {
            position: fixed;
            right: 2rem;
            top: 50%;
            transform: translateY(-50%);
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
            z-index: 1000;
        }

        .indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.5);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .indicator.active {
            background: var(--primary-blue);
            transform: scale(1.2);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .slide {
                width: 95vw;
                height: 90vh;
            }

            .slide-content {
                padding: 2rem;
            }

            .cover-title {
                font-size: 2.5rem;
            }

            .slide-title {
                font-size: 2rem;
            }

            .content-grid {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .stats-container {
                grid-template-columns: repeat(2, 1fr);
                gap: 1rem;
            }

            .navigation {
                padding: 0.8rem 1.5rem;
            }

            .nav-button {
                width: 40px;
                height: 40px;
                font-size: 1rem;
            }
        }
    </style>
</head>
<body>
    <div class="presentation-container">
        <!-- 第1页：封面 -->
        <div class="slide cover-slide active" id="slide-1">
            <div class="slide-content">
                <h1 class="cover-title">茂名市地质灾害预警平台</h1>
                <p class="cover-subtitle">守护茂名市民的地质安全</p>
                <div class="cover-info">
                    <div><i class="fas fa-building"></i>茂名市自然资源勘探测绘院</div>
                    <div><i class="fas fa-calendar"></i>2025年7月</div>
                    <div><i class="fas fa-users"></i>服务472万茂名市民</div>
                </div>
            </div>
        </div>

        <!-- 第2页：产品概述 -->
        <div class="slide" id="slide-2">
            <div class="slide-content">
                <h2 class="slide-title">产品概述</h2>
                <p class="slide-subtitle">茂名市地质灾害预警平台致力于成为茂名市民身边的地质安全守护者</p>

                <div class="stats-container">
                    <div class="stat-item">
                        <span class="stat-number">74,215</span>
                        <div class="stat-label">地质灾害点和风险防范区</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">90</span>
                        <div class="stat-label">覆盖镇街</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">472万</span>
                        <div class="stat-label">服务人口</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">99.5%</span>
                        <div class="stat-label">系统可用性目标</div>
                    </div>
                </div>

                <div style="background: linear-gradient(135deg, var(--light-blue), var(--bg-white)); padding: 2rem; border-radius: 12px; text-align: center; margin-top: 2rem;">
                    <h3 style="color: var(--primary-blue); margin-bottom: 1rem; font-size: 1.3rem;">产品愿景</h3>
                    <p style="color: var(--text-secondary); line-height: 1.6;">通过专业的数据管理和便民的查询服务，让地质灾害风险信息触手可及，让安全防护深入人心</p>
                </div>
            </div>
        </div>

        <!-- 第3页：核心功能 -->
        <div class="slide" id="slide-3">
            <div class="slide-content">
                <h2 class="slide-title">核心功能模块</h2>
                <p class="slide-subtitle">四大核心模块，构建完整的地质灾害预警体系</p>

                <div class="content-grid">
                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-search-location"></i>
                        </div>
                        <h3 class="card-title">公众查询服务</h3>
                        <p class="card-description">基于天地图服务的便民化查询体验，响应时间小于3秒</p>
                        <ul class="card-features">
                            <li>网站查询服务</li>
                            <li>微信公众号查询</li>
                            <li>位置定位查询</li>
                            <li>专业化风险展示</li>
                        </ul>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-database"></i>
                        </div>
                        <h3 class="card-title">数据管理模块</h3>
                        <p class="card-description">支持74,215个地质灾害点的完整生命周期管理</p>
                        <ul class="card-features">
                            <li>地质灾害点管理</li>
                            <li>风险防范区管理</li>
                            <li>数据导入导出</li>
                            <li>数据质量控制</li>
                        </ul>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-broadcast-tower"></i>
                        </div>
                        <h3 class="card-title">预警发布机制</h3>
                        <p class="card-description">多渠道预警发布，覆盖率达到95%以上</p>
                        <ul class="card-features">
                            <li>微信公众号发布（优先）</li>
                            <li>网站公告发布</li>
                            <li>短信发布（应急补充）</li>
                            <li>预警效果跟踪</li>
                        </ul>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-shield-alt"></i>
                        </div>
                        <h3 class="card-title">系统管理模块</h3>
                        <p class="card-description">完整的系统管理体系，确保平台安全可靠</p>
                        <ul class="card-features">
                            <li>用户认证与授权</li>
                            <li>权限管理体系</li>
                            <li>操作审计机制</li>
                            <li>系统监控告警</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第4页：技术架构 -->
        <div class="slide" id="slide-4">
            <div class="slide-content">
                <h2 class="slide-title">技术架构</h2>
                <p class="slide-subtitle">现代化技术栈，确保系统稳定可靠</p>

                <div class="chart-container">
                    <div id="techChart" style="width: 100%; height: 100%;"></div>
                </div>

                <div class="content-grid" style="grid-template-columns: 1fr 1fr; margin-top: 2rem;">
                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-layer-group"></i>
                        </div>
                        <h3 class="card-title">技术栈</h3>
                        <ul class="card-features">
                            <li><strong>前端：</strong>Vue3 + Element Plus + Vite</li>
                            <li><strong>后端：</strong>Python FastAPI + Nginx</li>
                            <li><strong>数据库：</strong>MySQL 8.0 + MongoDB 6.0</li>
                            <li><strong>地图服务：</strong>天地图API</li>
                        </ul>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-cloud"></i>
                        </div>
                        <h3 class="card-title">基础设施</h3>
                        <ul class="card-features">
                            <li><strong>服务器：</strong>16核64G云服务器</li>
                            <li><strong>存储：</strong>100GB系统盘 + 500GB数据盘</li>
                            <li><strong>网络：</strong>30Mbps固定带宽</li>
                            <li><strong>成本：</strong>9,676.8元至37,433.21元/年</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第5页：实施路线图 -->
        <div class="slide" id="slide-5">
            <div class="slide-content">
                <h2 class="slide-title">实施路线图</h2>
                <p class="slide-subtitle">分阶段实施，确保项目成功交付</p>

                <div class="chart-container">
                    <div id="roadmapChart" style="width: 100%; height: 100%;"></div>
                </div>

                <div class="content-grid">
                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-rocket"></i>
                        </div>
                        <h3 class="card-title">第一阶段（1-2周）</h3>
                        <p class="card-description">公众查询服务快速上线</p>
                        <ul class="card-features">
                            <li>网站查询服务</li>
                            <li>查询结果展示</li>
                            <li>微信公众号集成</li>
                        </ul>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-cogs"></i>
                        </div>
                        <h3 class="card-title">第二阶段（3-10周）</h3>
                        <p class="card-description">系统管理和数据管理体系建设</p>
                        <ul class="card-features">
                            <li>用户认证与权限管理</li>
                            <li>数据管理模块</li>
                            <li>系统监控与日志</li>
                        </ul>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-broadcast-tower"></i>
                        </div>
                        <h3 class="card-title">第三阶段（10-13周）</h3>
                        <p class="card-description">预警发布机制完善</p>
                        <ul class="card-features">
                            <li>多渠道预警发布</li>
                            <li>预警效果跟踪</li>
                            <li>系统优化与测试</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第6页：产品优势 -->
        <div class="slide" id="slide-6">
            <div class="slide-content">
                <h2 class="slide-title">产品优势</h2>
                <p class="slide-subtitle">专业化、技术化、便民化的一体化解决方案</p>

                <div class="content-grid">
                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-bullseye"></i>
                        </div>
                        <h3 class="card-title">专业化优势</h3>
                        <p class="card-description">专注地质灾害垂直领域，避免功能冗余</p>
                        <ul class="card-features">
                            <li>深度结合茂名市实际需求</li>
                            <li>专业化的一体化解决方案</li>
                            <li>避免通用系统的功能冗余</li>
                        </ul>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-code"></i>
                        </div>
                        <h3 class="card-title">技术优势</h3>
                        <p class="card-description">现代化技术栈，轻量化设计</p>
                        <ul class="card-features">
                            <li>系统可用性达到99.5%以上</li>
                            <li>查询响应时间小于3秒</li>
                            <li>轻量化设计，资源消耗少</li>
                        </ul>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-users"></i>
                        </div>
                        <h3 class="card-title">用户体验优势</h3>
                        <p class="card-description">便民化设计，操作简洁直观</p>
                        <ul class="card-features">
                            <li>网站和微信公众号双渠道</li>
                            <li>无需培训即可使用</li>
                            <li>专业化风险展示</li>
                        </ul>
                    </div>

                    <div class="content-card">
                        <div class="card-icon">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <h3 class="card-title">成本优势</h3>
                        <p class="card-description">自主研发，运营成本可控</p>
                        <ul class="card-features">
                            <li>优先使用低成本预警渠道</li>
                            <li>轻量化设计降低运行成本</li>
                            <li>自主研发避免授权费用</li>
                        </ul>
                    </div>
                </div>

                <div class="chart-container" style="height: 300px; margin-top: 2rem;">
                    <div id="advantageChart" style="width: 100%; height: 100%;"></div>
                </div>
            </div>
        </div>

        <!-- 第7页：成功指标与价值 -->
        <div class="slide" id="slide-7">
            <div class="slide-content">
                <h2 class="slide-title">成功指标与价值</h2>
                <p class="slide-subtitle">量化指标衡量项目成功，社会价值彰显项目意义</p>

                <div class="stats-container">
                    <div class="stat-item">
                        <span class="stat-number">100%</span>
                        <div class="stat-label">查询服务功能完整性</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">95%+</span>
                        <div class="stat-label">预警信息发布覆盖率</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">50%+</span>
                        <div class="stat-label">数据管理效率提升</div>
                    </div>
                    <div class="stat-item">
                        <span class="stat-number">&lt;3秒</span>
                        <div class="stat-label">查询响应时间</div>
                    </div>
                </div>

                <div class="chart-container">
                    <div id="valueChart" style="width: 100%; height: 100%;"></div>
                </div>

                <div style="background: linear-gradient(135deg, var(--success-green), var(--accent-blue)); color: white; padding: 2rem; border-radius: 12px; text-align: center; margin-top: 2rem;">
                    <h3 style="margin-bottom: 1rem; font-size: 1.3rem;">社会价值</h3>
                    <p style="line-height: 1.6; opacity: 0.95;">提升公众安全防护能力，增强政府服务透明度和公信力，为茂名市数字政府建设贡献力量，具有重要的社会价值和现实意义</p>
                </div>
            </div>
        </div>

        <!-- 第8页：封底 -->
        <div class="slide cover-slide" id="slide-8">
            <div class="slide-content">
                <h1 class="cover-title">谢谢观看</h1>
                <p class="cover-subtitle">让地质灾害风险信息触手可及<br>让安全防护深入人心</p>
                <div class="cover-info">
                    <div><i class="fas fa-envelope"></i>联系邮箱：<EMAIL></div>
                    <div><i class="fas fa-phone"></i>技术支持：茂名市自然资源勘探测绘院</div>
                    <div><i class="fas fa-heart"></i>守护茂名，我们在行动</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 导航控制 -->
    <div class="navigation">
        <button class="nav-button" id="prevBtn" onclick="changeSlide(-1)">
            <i class="fas fa-chevron-left"></i>
        </button>
        <div class="slide-counter">
            <span id="currentSlide">1</span> / <span id="totalSlides">8</span>
        </div>
        <button class="nav-button" id="nextBtn" onclick="changeSlide(1)">
            <i class="fas fa-chevron-right"></i>
        </button>
    </div>

    <!-- 幻灯片指示器 -->
    <div class="slide-indicators">
        <div class="indicator active" onclick="goToSlide(1)"></div>
        <div class="indicator" onclick="goToSlide(2)"></div>
        <div class="indicator" onclick="goToSlide(3)"></div>
        <div class="indicator" onclick="goToSlide(4)"></div>
        <div class="indicator" onclick="goToSlide(5)"></div>
        <div class="indicator" onclick="goToSlide(6)"></div>
        <div class="indicator" onclick="goToSlide(7)"></div>
        <div class="indicator" onclick="goToSlide(8)"></div>
    </div>

    <script>
        let currentSlideIndex = 1;
        const totalSlides = 8;

        // 幻灯片导航功能
        function changeSlide(direction) {
            const currentSlide = document.getElementById(`slide-${currentSlideIndex}`);
            const indicators = document.querySelectorAll('.indicator');

            currentSlide.classList.remove('active');
            indicators[currentSlideIndex - 1].classList.remove('active');

            currentSlideIndex += direction;

            if (currentSlideIndex > totalSlides) {
                currentSlideIndex = 1;
            } else if (currentSlideIndex < 1) {
                currentSlideIndex = totalSlides;
            }

            const newSlide = document.getElementById(`slide-${currentSlideIndex}`);
            newSlide.classList.add('active');
            indicators[currentSlideIndex - 1].classList.add('active');

            updateSlideCounter();
            updateNavButtons();
            initializeCharts();
        }

        function goToSlide(slideNumber) {
            const currentSlide = document.getElementById(`slide-${currentSlideIndex}`);
            const indicators = document.querySelectorAll('.indicator');

            currentSlide.classList.remove('active');
            indicators[currentSlideIndex - 1].classList.remove('active');

            currentSlideIndex = slideNumber;

            const newSlide = document.getElementById(`slide-${currentSlideIndex}`);
            newSlide.classList.add('active');
            indicators[currentSlideIndex - 1].classList.add('active');

            updateSlideCounter();
            updateNavButtons();
            initializeCharts();
        }

        function updateSlideCounter() {
            document.getElementById('currentSlide').textContent = currentSlideIndex;
        }

        function updateNavButtons() {
            const prevBtn = document.getElementById('prevBtn');
            const nextBtn = document.getElementById('nextBtn');

            // 可以循环播放，所以按钮始终可用
            prevBtn.disabled = false;
            nextBtn.disabled = false;
        }

        // 键盘导航
        document.addEventListener('keydown', function(event) {
            if (event.key === 'ArrowLeft') {
                changeSlide(-1);
            } else if (event.key === 'ArrowRight') {
                changeSlide(1);
            } else if (event.key === 'Home') {
                goToSlide(1);
            } else if (event.key === 'End') {
                goToSlide(totalSlides);
            }
        });

        // 初始化图表
        function initializeCharts() {
            setTimeout(() => {
                if (currentSlideIndex === 4) {
                    initTechChart();
                } else if (currentSlideIndex === 5) {
                    initRoadmapChart();
                } else if (currentSlideIndex === 6) {
                    initAdvantageChart();
                } else if (currentSlideIndex === 7) {
                    initValueChart();
                }
            }, 100);
        }

        // 技术架构图表
        function initTechChart() {
            const chartDom = document.getElementById('techChart');
            if (!chartDom) return;

            const myChart = echarts.init(chartDom);
            const option = {
                title: {
                    text: '技术架构层次',
                    left: 'center',
                    textStyle: {
                        color: '#1E40AF',
                        fontSize: 18,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c}% ({d}%)'
                },
                series: [
                    {
                        name: '技术架构',
                        type: 'pie',
                        radius: ['40%', '70%'],
                        center: ['50%', '60%'],
                        data: [
                            { value: 30, name: '表现层\n(Vue3 + Element Plus)', itemStyle: { color: '#3B82F6' } },
                            { value: 40, name: '业务逻辑层\n(Python FastAPI)', itemStyle: { color: '#1E40AF' } },
                            { value: 30, name: '数据存储层\n(MySQL + MongoDB)', itemStyle: { color: '#06B6D4' } }
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        label: {
                            show: true,
                            position: 'outside',
                            formatter: '{b}',
                            fontSize: 12,
                            color: '#1F2937'
                        }
                    }
                ]
            };
            myChart.setOption(option);

            // 响应式处理
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }

        // 实施路线图表
        function initRoadmapChart() {
            const chartDom = document.getElementById('roadmapChart');
            if (!chartDom) return;

            const myChart = echarts.init(chartDom);
            const option = {
                title: {
                    text: '项目实施进度',
                    left: 'center',
                    textStyle: {
                        color: '#1E40AF',
                        fontSize: 18,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'shadow'
                    },
                    formatter: function(params) {
                        return params[0].name + '<br/>' +
                               params[0].seriesName + ': ' + params[0].value + '%';
                    }
                },
                grid: {
                    left: '3%',
                    right: '4%',
                    bottom: '3%',
                    containLabel: true
                },
                xAxis: {
                    type: 'category',
                    data: ['第1-2周', '第3-6周', '第7-10周', '第11-13周'],
                    axisLabel: {
                        color: '#6B7280'
                    }
                },
                yAxis: {
                    type: 'value',
                    name: '完成度(%)',
                    axisLabel: {
                        color: '#6B7280'
                    }
                },
                series: [
                    {
                        name: '项目进度',
                        type: 'bar',
                        data: [
                            { value: 25, itemStyle: { color: '#10B981' } },
                            { value: 60, itemStyle: { color: '#3B82F6' } },
                            { value: 85, itemStyle: { color: '#F59E0B' } },
                            { value: 100, itemStyle: { color: '#1E40AF' } }
                        ],
                        barWidth: '60%',
                        label: {
                            show: true,
                            position: 'top',
                            formatter: '{c}%',
                            color: '#1F2937'
                        }
                    }
                ]
            };
            myChart.setOption(option);

            // 响应式处理
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }

        // 产品优势雷达图
        function initAdvantageChart() {
            const chartDom = document.getElementById('advantageChart');
            if (!chartDom) return;

            const myChart = echarts.init(chartDom);
            const option = {
                title: {
                    text: '产品优势评估',
                    left: 'center',
                    textStyle: {
                        color: '#1E40AF',
                        fontSize: 18,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item'
                },
                radar: {
                    indicator: [
                        { name: '专业化程度', max: 100 },
                        { name: '技术先进性', max: 100 },
                        { name: '用户体验', max: 100 },
                        { name: '成本控制', max: 100 },
                        { name: '安全性', max: 100 },
                        { name: '可维护性', max: 100 }
                    ],
                    center: ['50%', '60%'],
                    radius: '70%',
                    axisName: {
                        color: '#6B7280'
                    }
                },
                series: [
                    {
                        name: '产品优势',
                        type: 'radar',
                        data: [
                            {
                                value: [95, 90, 88, 85, 92, 87],
                                name: '茂名地质灾害预警平台',
                                itemStyle: {
                                    color: '#3B82F6'
                                },
                                areaStyle: {
                                    color: 'rgba(59, 130, 246, 0.3)'
                                }
                            }
                        ]
                    }
                ]
            };
            myChart.setOption(option);

            // 响应式处理
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }

        // 价值分析图表
        function initValueChart() {
            const chartDom = document.getElementById('valueChart');
            if (!chartDom) return;

            const myChart = echarts.init(chartDom);
            const option = {
                title: {
                    text: '社会价值分析',
                    left: 'center',
                    textStyle: {
                        color: '#1E40AF',
                        fontSize: 18,
                        fontWeight: 'bold'
                    }
                },
                tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b}: {c}万人 ({d}%)'
                },
                series: [
                    {
                        name: '受益人群',
                        type: 'pie',
                        radius: ['30%', '70%'],
                        center: ['50%', '60%'],
                        data: [
                            { value: 472, name: '直接受益群众', itemStyle: { color: '#10B981' } },
                            { value: 100, name: '政府工作人员', itemStyle: { color: '#3B82F6' } },
                            { value: 50, name: '基层工作人员', itemStyle: { color: '#F59E0B' } },
                            { value: 30, name: '相关从业人员', itemStyle: { color: '#06B6D4' } }
                        ],
                        emphasis: {
                            itemStyle: {
                                shadowBlur: 10,
                                shadowOffsetX: 0,
                                shadowColor: 'rgba(0, 0, 0, 0.5)'
                            }
                        },
                        label: {
                            show: true,
                            position: 'outside',
                            formatter: '{b}\n{c}万人',
                            fontSize: 12,
                            color: '#1F2937'
                        }
                    }
                ]
            };
            myChart.setOption(option);

            // 响应式处理
            window.addEventListener('resize', function() {
                myChart.resize();
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateSlideCounter();
            updateNavButtons();
            initializeCharts();
        });

        // 自动播放功能（可选）
        let autoPlay = false;
        let autoPlayInterval;

        function toggleAutoPlay() {
            if (autoPlay) {
                clearInterval(autoPlayInterval);
                autoPlay = false;
                console.log('自动播放已关闭');
            } else {
                autoPlayInterval = setInterval(() => {
                    changeSlide(1);
                }, 5000);
                autoPlay = true;
                console.log('自动播放已开启，每5秒切换一次');
            }
        }

        // 双击切换自动播放
        document.addEventListener('dblclick', toggleAutoPlay);
    </script>
</body>
</html>
